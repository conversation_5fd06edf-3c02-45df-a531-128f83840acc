import axios from 'axios';

const apiClient = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    // baseURL: 'http://192.168.88.71:3000/api',
    headers: {
        'Content-Type': 'application/json',
    },
});

// Debug: Log the base URL being used
console.log('API Base URLs:', import.meta.env.VITE_API_BASE_URL);

apiClient.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

export default apiClient;