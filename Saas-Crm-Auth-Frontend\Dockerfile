# ---------- Build Stage ----------
FROM node:20-alpine AS build

WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --no-audit --no-fund

COPY . .

# Use production env for build; Vite reads VITE_* at build time
ARG NODE_ENV=production
ENV NODE_ENV=$NODE_ENV

# Pass build-time environment variables
ARG VITE_API_BASE_URL
ARG VITE_LOGIN_URL
ARG VITE_API_BASE_URL_AUTH
ARG VITE_LOGIN_REDIRECT

ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_LOGIN_URL=$VITE_LOGIN_URL
ENV VITE_API_BASE_URL_AUTH=$VITE_API_BASE_URL_AUTH
ENV VITE_LOGIN_REDIRECT=$VITE_LOGIN_REDIRECT

# Debug: Show environment variables
RUN echo "VITE_API_BASE_URL=$VITE_API_BASE_URL"
RUN echo "VITE_LOGIN_URL=$VITE_LOGIN_URL"
RUN echo "VITE_API_BASE_URL_AUTH=$VITE_API_BASE_URL_AUTH"
RUN echo "VITE_LOGIN_REDIRECT=$VITE_LOGIN_REDIRECT"

RUN npm run build

# ---------- Runtime Stage (Nginx) ----------
FROM node:20-alpine AS runtime

WORKDIR /app

# Install a simple static file server
RUN npm i -g serve

# Copy built assets
COPY --from=build /app/dist /app/dist

# Expose application port
EXPOSE 4006

# Healthcheck (optional)
HEALTHCHECK --interval=30s --timeout=3s --retries=3 CMD wget -qO- http://localhost:4006/ || exit 1

CMD ["serve", "-s", "dist", "-l", "4006"]


